'use client'

import { useState, useEffect, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client' 
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/utils'
import { Plus, Minus, ShoppingCart, Coffee, Loader2, X } from 'lucide-react'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll'
import { useDynamicFilterOptions } from '@/hooks/use-dynamic-filter-options'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import { getCartManager } from '@/lib/cart'
import { safeGetUser } from '@/lib/supabase/helpers'
import { useTranslations, useLocale } from 'next-intl'


interface Product {
  id: string
  title: string
  description: string
  category: string
  coffee_type: string | null
  brand: string | null
  blend: string | null
  machine_compatibility: string[] | null
  pack_quantity: number | null
  pack_weight_grams: number | null
  price: number
  discount_price: number | null
  cost_per_espresso: number | null
  images: string[]
  is_available: boolean
}

interface CoffeeBoxItem {
  product: Product
  quantity: number
}

interface CoffeeBoxBuilderProps {
  initialProducts: Product[]
}

export function CoffeeBoxBuilder({ initialProducts }: CoffeeBoxBuilderProps) {
  const [modalProduct, setModalProduct] = useState<Product | null>(null)
  const [selectedItems, setSelectedItems] = useState<CoffeeBoxItem[]>([])
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterBrand, setFilterBrand] = useState<string>('all')
  const [filterBlend, setFilterBlend] = useState<string>('all')
  const [filterCompatibility, setFilterCompatibility] = useState<string>('all')
  const [pointsPerChf, setPointsPerChf] = useState<number>(1)
  const [freeShippingThreshold, setFreeShippingThreshold] = useState<number>(90)
  const [shippingCost, setShippingCost] = useState<number>(8.90)
  const [levelPointThresholds, setLevelPointThresholds] = useState<number[]>([50, 100, 170, 250, 350, 470, 620, 800, 1020, 1280])
  const [currentUserPoints, setCurrentUserPoints] = useState<number>(0)

  // Get dynamic filter options based on current filters
  const { filterOptions } = useDynamicFilterOptions({
    category: filterCategory,
    coffeeType: filterType,
    brand: filterBrand
  })

  // Reset dependent filters when parent filters change
  const handleCategoryChange = (value: string) => {
    setFilterCategory(value)
    setFilterType('all')
    setFilterBrand('all')
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const handleTypeChange = (value: string) => {
    setFilterType(value)
    setFilterBrand('all')
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const handleBrandChange = (value: string) => {
    setFilterBrand(value)
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const supabase = createClient()
  const locale = useLocale()

  useEffect(() => {
    const loadData = async () => {
      // Load points per CHF setting
      const { data: settingsData, error: settingsError } = await supabase
        .from('site_settings')
        .select('points_per_chf')
        .maybeSingle()
      if (!settingsError && settingsData?.points_per_chf) {
        setPointsPerChf(parseFloat(settingsData.points_per_chf))
      }

      // Load shipping settings for Switzerland (CH)
      const { data: shippingData, error: shippingError } = await supabase
        .from('shipping_rates')
        .select('free_shipping_threshold, cost')
        .eq('country', 'CH')
        .eq('is_active', true)
        .maybeSingle()
      if (!shippingError && shippingData) {
        if (shippingData.free_shipping_threshold) {
          setFreeShippingThreshold(shippingData.free_shipping_threshold)
        }
        if (shippingData.cost) {
          setShippingCost(shippingData.cost)
        }
      }

      // Load league configuration for level thresholds
      const { data: leagueData, error: leagueError } = await supabase
        .from('league_config')
        .select('levels_per_league, league_1_points, league_2_points')
        .eq('is_active', true)
        .maybeSingle()
      if (!leagueError && leagueData) {
        // Calculate level thresholds for first league (levels 1-10)
        const levelsPerLeague = leagueData.levels_per_league || 10
        const league1Points = leagueData.league_1_points || 0
        const league2Points = leagueData.league_2_points || 1000

        // Calculate progressive thresholds within first league
        const pointsPerLevel = (league2Points - league1Points) / levelsPerLeague
        const thresholds = []
        for (let i = 1; i <= levelsPerLeague; i++) {
          thresholds.push(Math.round(league1Points + (pointsPerLevel * i)))
        }
        setLevelPointThresholds(thresholds)
      }

      // Load current user points if logged in
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('total_points')
            .eq('id', user.id)
            .single()

          if (!userError && userData) {
            setCurrentUserPoints(userData.total_points || 0)
          }
        } else {
          // User not logged in, set to 0 (level 0)
          setCurrentUserPoints(0)
        }
      } catch {
        // Handle auth errors gracefully - user not logged in
        setCurrentUserPoints(0)
      }
    }
    loadData()
  }, [supabase])
  const router = useRouter()
  const { toast } = useToast()
  const t = useTranslations('common')
  const tc = useTranslations('cart')
  const tb = useTranslations('coffeeBoxBuilder')
  const ts = useTranslations('shop')

  // Filter products
  const handleOpenProduct = (product: Product) => {
    setModalProduct(product)
    // Debug: Log product data in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Modal Product Data:', product)
    }
  }
  const closeModal = () => setModalProduct(null)

  // Infinite scroll with filters
  const filters = useMemo(() => ({
    category: filterCategory,
    coffee_type: filterType,
    brand: filterBrand,
    blend: filterBlend,
    compatibility: filterCompatibility
  }), [filterCategory, filterType, filterBrand, filterBlend, filterCompatibility])

  const { products, loading, hasMore, loadMore } = useInfiniteScroll({
    initialProducts,
    filters,
    limit: 20,
    locale
  })

  // Remove intersection observer - using manual load more button instead

  // Use filter options from all products
  const categories = filterOptions.categories
  const coffeeTypes = filterOptions.coffeeTypes
  const brands = filterOptions.brands
  const blends = filterOptions.blends
  const compatibilities = filterOptions.compatibilities

  // Calculate totals
  const totals = useMemo(() => {
    const subtotal = selectedItems.reduce((sum, item) => {
      const price = item.product.discount_price || item.product.price
      return sum + (price * item.quantity)
    }, 0)

    const calculatedShippingCost = subtotal >= freeShippingThreshold ? 0 : shippingCost
    const total = subtotal + calculatedShippingCost

    // Points to next level (simplified for league system)
    const pointsFromOrder = subtotal * pointsPerChf
    const totalPointsAfterOrder = currentUserPoints + pointsFromOrder

    // Use dynamic level thresholds
    const nextLevelPoints = levelPointThresholds.find(th => totalPointsAfterOrder < th)
    const pointsProgress = nextLevelPoints ? (totalPointsAfterOrder / nextLevelPoints) * 100 : 100
    const remainingPoints = nextLevelPoints ? nextLevelPoints - totalPointsAfterOrder : 0

    return {
      subtotal,
      shippingCost: calculatedShippingCost,
      total,
      freeShippingProgress: Math.min((subtotal / freeShippingThreshold) * 100, 100),
      nextLevelPoints,
      pointsProgress,
      remainingPoints
    }
  }, [selectedItems, pointsPerChf, freeShippingThreshold, shippingCost, levelPointThresholds, currentUserPoints])



  const addProduct = async (product: Product) => {
    const cartManager = getCartManager()
    const supabase = createClient()

    try {
      // Get current user
      const user = await safeGetUser(supabase)

      const success = await cartManager.addToCart(product.id, 1, user?.id)

      if (success) {
        toast({
          title: t('added'),
          description: t('productAddedToCart'),
        })

        // Trigger cart refresh event for other components
        window.dispatchEvent(new CustomEvent('cartUpdated'))

        // Also update local selectedItems for UI display
        setSelectedItems(prev => {
          const existing = prev.find(item => item.product.id === product.id)
          if (existing) {
            return prev.map(item =>
              item.product.id === product.id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            )
          } else {
            return [...prev, { product, quantity: 1 }]
          }
        })
      } else {
        toast({
          title: t('error'),
          description: t('addToCartError'),
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast({
        title: t('error'),
        description: t('addToCartError'),
        variant: "destructive",
      })
    }
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      setSelectedItems(prev => prev.filter(item => item.product.id !== productId))
    } else {
      setSelectedItems(prev =>
        prev.map(item =>
          item.product.id === productId
            ? { ...item, quantity }
            : item
        )
      )
    }
  }

  const goToCart = () => {
    router.push(`/${locale}/cart`)
  }

  return (
    <>
    <div className="grid lg:grid-cols-3 gap-8">
      {/* Product Selection */}
      <div className="lg:col-span-2 space-y-6">
        {/* Filters */}
        <Card className="border-0 bg-gradient-to-r from-white to-gray-50/50 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Coffee className="h-5 w-5 text-amber-600" />
              {tb('filters.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.category')}</label>
                <Select value={filterCategory} onValueChange={handleCategoryChange}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allCategories')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allCategories')}</SelectItem>
                    {categories.map(category => (
                       <SelectItem key={category} value={category}>
                         {category === 'coffee' ? ts('coffee') : ts('accessories')}
                       </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.coffeeType')}</label>
                <Select value={filterType} onValueChange={handleTypeChange}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allTypes')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allTypes')}</SelectItem>
                    {coffeeTypes.map(type => {
                      const translatedType = tb(`coffeeTypes.${type}`, { defaultValue: type })
                      return (
                        <SelectItem key={type} value={type}>
                          {translatedType}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.brand')}</label>
                <Select value={filterBrand} onValueChange={handleBrandChange}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allBrands')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allBrands')}</SelectItem>
                    {brands.map(brand => (
                      <SelectItem key={brand} value={brand}>
                        {brand}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.blend')}</label>
                <Select value={filterBlend} onValueChange={setFilterBlend}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allBlends')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allBlends')}</SelectItem>
                    {blends.map(blend => (
                      <SelectItem key={blend} value={blend}>
                        {blend}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block">{tb('filters.compatibility')}</label>
                <Select value={filterCompatibility} onValueChange={setFilterCompatibility}>
                  <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                    <SelectValue placeholder={tb('filters.allCompatibilities')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tb('filters.allCompatibilities')}</SelectItem>
                    {compatibilities.map(compatibility => (
                      <SelectItem key={compatibility} value={compatibility}>
                        {compatibility}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        {loading && products.length === 0 ? (
          /* Loading State */
          <div className="text-center py-16">
            <Loader2 className="h-16 w-16 text-muted-foreground mx-auto mb-4 animate-spin" />
            <h3 className="text-2xl font-semibold mb-2">{tb('loading', { defaultValue: 'Loading products...' })}</h3>
            <p className="text-muted-foreground">
              {tb('loadingDescription', { defaultValue: 'Please wait while we load the products for you.' })}
            </p>
          </div>
        ) : products.length === 0 ? (
          /* Empty State */
          <div className="text-center py-16">
            <Coffee className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-2xl font-semibold mb-2">{tb('noProducts', { defaultValue: 'No products found' })}</h3>
            <p className="text-muted-foreground">
              {tb('noProductsDescription', { defaultValue: 'Try adjusting your filters to see more products.' })}
            </p>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 gap-6">
            {products.map(product => {
            const selectedItem = selectedItems.find(item => item.product.id === product.id)

            return (
              <Card 
                key={product.id} 
                className="group relative cursor-pointer overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50/80 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => handleOpenProduct(product)}
              >
                <CardContent className="p-0">
                  <div className="relative">
                    {/* Background gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Discount badge */}
                    {product.discount_price && product.discount_price > 0 && product.discount_price < product.price && (
                      <div className="absolute top-3 right-3 z-10">
                        <Badge className="bg-red-500 text-white text-xs font-medium px-2 py-1 shadow-lg">
                          -{Math.round(((product.price - product.discount_price) / product.price) * 100)}%
                        </Badge>
                      </div>
                    )}

                    <div className="flex gap-4 p-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-sm group-hover:shadow-md transition-shadow duration-300">
                          {product.images && product.images[0] ? (
                            <Image
                              src={product.images[0]}
                              alt={product.title}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-amber-50 to-amber-100">
                              <Coffee className="h-8 w-8 text-amber-600/70" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="mb-3">
                          <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-amber-700 transition-colors duration-200">
                            {product.title}
                          </h3>
                          {product.brand && (
                            <p className="text-sm text-gray-500 font-medium">{product.brand}</p>
                          )}
                        </div>

                        {/* Product badges */}
                        <div className="flex items-center gap-2 mb-4 flex-wrap">
                          {product.coffee_type && (
                            <Badge variant="secondary" className="text-xs bg-amber-50 text-amber-700 border-amber-200 font-medium">
                              {tb(`coffeeTypes.${product.coffee_type}`, { defaultValue: product.coffee_type })}
                            </Badge>
                          )}
                          {(product.coffee_type === 'capsules' || product.coffee_type === 'pods') && product.pack_quantity && (
                            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                              {product.pack_quantity} {ts('product.pieces')}
                            </Badge>
                          )}
                          {(product.coffee_type === 'ground' || product.coffee_type === 'beans') && product.pack_weight_grams && (
                            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                              {product.pack_weight_grams} g
                            </Badge>
                          )}
                        </div>
                        
                        {/* Price and actions */}
                        <div className="flex items-end justify-between">
                          <div className="space-y-1">
                            {product.discount_price && product.discount_price > 0 && product.discount_price < product.price ? (
                              <div className="space-y-1">
                                <div className="text-sm text-gray-400 line-through font-medium">
                                  {formatCurrency(product.price)}
                                </div>
                                <div className="font-bold text-lg text-gray-900">
                                  {formatCurrency(product.discount_price)}
                                </div>
                              </div>
                            ) : (
                              <div className="font-bold text-lg text-gray-900">
                                {formatCurrency(product.price)}
                              </div>
                            )}
                            {product.cost_per_espresso && product.cost_per_espresso > 0 && (
                              <div className="text-xs text-emerald-600 font-medium bg-emerald-50 px-2 py-1 rounded-full inline-block">
                                {formatCurrency(product.cost_per_espresso)}/{ts('product.perEspresso')}
                              </div>
                            )}
                          </div>
                          
                          {/* Quantity controls or add button */}
                          {selectedItem ? (
                            <div className="flex items-center gap-2 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => { e.stopPropagation(); updateQuantity(product.id, selectedItem.quantity - 1) }}
                                className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="text-sm font-semibold px-3 py-1 bg-gray-50 rounded text-gray-900 min-w-[2rem] text-center">
                                {selectedItem.quantity}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => { e.stopPropagation(); updateQuantity(product.id, selectedItem.quantity + 1) }}
                                className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors duration-200"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => { e.stopPropagation(); addProduct(product) }}
                              className="h-9 px-4 bg-white hover:bg-amber-50 hover:border-amber-300 hover:text-amber-700 transition-all duration-200 shadow-sm"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              {t('add')}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
          </div>
        )}

        {/* Load More Button */}
        {hasMore && (
          <div className="flex justify-center py-8">
            <Button
              onClick={loadMore}
              disabled={loading}
              variant="outline"
              size="lg"
              className="min-w-[200px]"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {tb('loading', { defaultValue: 'Loading...' })}
                </>
              ) : (
                tb('loadMore')
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Summary Sidebar */}
      <div className="space-y-6">
        {/* Progress Indicators */}
        {totals.subtotal < freeShippingThreshold && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{tc('freeShipping')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>{tb('remaining', { amount: formatCurrency(freeShippingThreshold - totals.subtotal) })}</span>
                  <span>{Math.round(totals.freeShippingProgress)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${totals.freeShippingProgress}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}



        {totals.nextLevelPoints && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{tb('nextLevel', { defaultValue: 'Prossimo livello' })}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span>{tb('remainingPoints', { amount: Math.round(totals.remainingPoints) })}</span>
                  <span>{Math.round(totals.pointsProgress)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full transition-all"
                    style={{ width: `${totals.pointsProgress}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">{tb('summary.title')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>{tc('subtotal')}</span>
              <span>{formatCurrency(totals.subtotal)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{tc('shipping')}</span>
              <span>
                {totals.shippingCost === 0 ? (
                  <span className="text-green-600 font-medium">{tc('free')}</span>
                ) : (
                  formatCurrency(totals.shippingCost)
                )}
              </span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between font-bold">
                <span>{tc('total')}</span>
                <span>{formatCurrency(totals.total)}</span>
              </div>
            </div>
            
            <Button
              onClick={goToCart}
              disabled={selectedItems.length === 0}
              className="w-full"
              size="lg"
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              {t('goToCart')}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>

    {/* Product Detail Modal - Redesigned for Mobile */}
    <Dialog open={!!modalProduct} onOpenChange={(open) => { if (!open) closeModal() }}>
      {modalProduct && (
        <DialogContent className="max-w-md sm:max-w-2xl lg:max-w-4xl max-h-[95vh] overflow-y-auto p-0 m-1 sm:m-4 rounded-2xl">
          <DialogTitle className="sr-only">{modalProduct.title}</DialogTitle>
          {/* Compact Header with Image */}
          <div className="relative">
            {/* Product Image as Header Background */}
            <div className="relative h-48 sm:h-56 overflow-hidden rounded-t-2xl bg-gradient-to-br from-gray-100 to-gray-200">
              {modalProduct.images && modalProduct.images[0] ? (
                <Image
                  src={modalProduct.images[0]}
                  alt={modalProduct.title}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Coffee className="h-16 w-16 text-gray-400" />
                </div>
              )}

              {/* Discount Badge */}
              {modalProduct.discount_price && modalProduct.discount_price > 0 && modalProduct.discount_price < modalProduct.price && (
                <div className="absolute top-3 left-3">
                  <Badge className="bg-red-500 text-white text-sm font-bold px-3 py-1 shadow-lg">
                    -{Math.round(((modalProduct.price - modalProduct.discount_price) / modalProduct.price) * 100)}%
                  </Badge>
                </div>
              )}

              {/* Availability Badge */}
              {!modalProduct.is_available && (
                <div className="absolute top-3 right-3">
                  <Badge variant="destructive" className="text-sm font-medium shadow-lg">
                    {t('unavailable')}
                  </Badge>
                </div>
              )}

              {/* Gradient Overlay for Text Readability */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

              {/* Product Title Overlay */}
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h2 className="text-xl sm:text-2xl font-bold leading-tight mb-1">
                  {modalProduct.title}
                </h2>
                {modalProduct.brand && (
                  <p className="text-white/90 font-medium">{modalProduct.brand}</p>
                )}
              </div>
            </div>

            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-all z-10"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          {/* Content Section */}
          <div className="p-4 sm:p-6 space-y-4">
            {/* Price and Tags Row */}
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-2 flex-wrap">
                <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                  {ts(modalProduct.category, { defaultValue: modalProduct.category })}
                </Badge>
                {modalProduct.coffee_type && (
                  <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                    {tb(`coffeeTypes.${modalProduct.coffee_type}`, { defaultValue: modalProduct.coffee_type })}
                  </Badge>
                )}
                {modalProduct.blend && (
                  <Badge variant="outline" className="border-green-300 text-green-700">
                    {modalProduct.blend}
                  </Badge>
                )}
              </div>

              {/* Price Section */}
              <div className="text-right">
                {modalProduct.discount_price && modalProduct.discount_price > 0 && modalProduct.discount_price < modalProduct.price ? (
                  <div className="space-y-0.5">
                    <div className="text-sm text-gray-400 line-through">
                      {formatCurrency(modalProduct.price)}
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {formatCurrency(modalProduct.discount_price)}
                    </div>
                  </div>
                ) : (
                  <div className="text-2xl font-bold text-gray-900">
                    {formatCurrency(modalProduct.price)}
                  </div>
                )}
                {modalProduct.cost_per_espresso && modalProduct.cost_per_espresso > 0 && (
                  <div className="text-xs text-emerald-600 font-medium bg-emerald-50 px-2 py-0.5 rounded-full mt-1">
                    {formatCurrency(modalProduct.cost_per_espresso)}/{ts('product.perEspresso')}
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            {modalProduct.description && (
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">Descrizione</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {modalProduct.description}
                </p>
              </div>
            )}

            {/* Compact Specifications Grid */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              {modalProduct.pack_weight_grams && modalProduct.pack_weight_grams > 0 && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Peso</div>
                  <div className="font-semibold text-gray-900">{modalProduct.pack_weight_grams} g</div>
                </div>
              )}
              {modalProduct.pack_quantity && modalProduct.pack_quantity > 0 && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Quantità</div>
                  <div className="font-semibold text-gray-900">{modalProduct.pack_quantity} {ts('product.pieces')}</div>
                </div>
              )}
              {modalProduct.coffee_type && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Tipo</div>
                  <div className="font-semibold text-gray-900">
                    {tb(`coffeeTypes.${modalProduct.coffee_type}`, { defaultValue: modalProduct.coffee_type })}
                  </div>
                </div>
              )}
              {modalProduct.blend && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Miscela</div>
                  <div className="font-semibold text-gray-900">{modalProduct.blend}</div>
                </div>
              )}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Categoria</div>
                <div className="font-semibold text-gray-900">
                  {ts(modalProduct.category, { defaultValue: modalProduct.category })}
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-gray-500 text-xs uppercase tracking-wide mb-1">Stato</div>
                <div className={`font-semibold ${modalProduct.is_available ? 'text-green-600' : 'text-red-600'}`}>
                  {modalProduct.is_available ? 'Disponibile' : 'Non disponibile'}
                </div>
              </div>
            </div>

            {/* Action Button - Modern Design */}
            <div className="pt-2">
              <Button
                onClick={(e) => { e.stopPropagation(); addProduct(modalProduct); closeModal(); }}
                disabled={!modalProduct.is_available}
                className="w-full h-12 text-base font-semibold bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <Plus className="h-5 w-5 mr-2" />
                {modalProduct.is_available ? t('add') : t('unavailable')}
              </Button>
            </div>
          </div>
        </DialogContent>
      )}
    </Dialog>
    </>
  )
}
