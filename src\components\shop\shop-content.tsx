'use client'

import { useState, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { ShoppingCart, Coffee, Loader2 } from 'lucide-react'
import { AddToCartButton } from '@/components/cart/add-to-cart-button'
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll'
import { useDynamicFilterOptions } from '@/hooks/use-dynamic-filter-options'

interface Product {
  id: string
  title: string
  description: string
  category: string
  coffee_type: string | null
  brand: string | null
  blend: string | null
  machine_compatibility: string[] | null
  pack_quantity: number | null
  pack_weight_grams: number | null
  price: number
  discount_price: number | null
  cost_per_espresso: number | null
  images: string[]
  is_available: boolean
}

interface ShopContentProps {
  initialProducts: Product[]
  locale: string
}

export function ShopContent({ initialProducts, locale }: ShopContentProps) {
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterBrand, setFilterBrand] = useState<string>('all')
  const [filterBlend, setFilterBlend] = useState<string>('all')
  const [filterCompatibility, setFilterCompatibility] = useState<string>('all')

  // Get dynamic filter options based on current filters
  const { filterOptions } = useDynamicFilterOptions({
    category: filterCategory,
    coffeeType: filterType,
    brand: filterBrand
  })

  // Reset dependent filters when parent filters change
  const handleCategoryChange = (value: string) => {
    setFilterCategory(value)
    setFilterType('all')
    setFilterBrand('all')
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const handleTypeChange = (value: string) => {
    setFilterType(value)
    setFilterBrand('all')
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const handleBrandChange = (value: string) => {
    setFilterBrand(value)
    setFilterBlend('all')
    setFilterCompatibility('all')
  }

  const t = useTranslations('shop')

  // Infinite scroll with filters
  const filters = useMemo(() => ({
    category: filterCategory,
    coffee_type: filterType,
    brand: filterBrand,
    blend: filterBlend,
    compatibility: filterCompatibility
  }), [filterCategory, filterType, filterBrand, filterBlend, filterCompatibility])

  const { products, loading, hasMore, loadMore } = useInfiniteScroll({
    initialProducts,
    filters,
    limit: 20,
    locale
  })

  // Remove intersection observer - using manual load more button instead

  // Use filter options from all products
  const categories = filterOptions.categories
  const coffeeTypes = filterOptions.coffeeTypes
  const brands = filterOptions.brands
  const blends = filterOptions.blends
  const compatibilities = filterOptions.compatibilities

  // Separate products by category for display
  const coffeeProducts = products.filter(p => p.category === 'coffee')
  const accessoryProducts = products.filter(p => p.category === 'accessories')

  return (
    <>
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      {/* Filters */}
      <Card className="mb-8 border-0 bg-gradient-to-r from-white to-gray-50/50 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Coffee className="h-5 w-5 text-amber-600" />
            {t('filters.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.category')}</label>
              <Select value={filterCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allCategories')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCategories')}</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'coffee' ? t('coffee') : t('accessories')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.coffeeType')}</label>
              <Select value={filterType} onValueChange={handleTypeChange}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allTypes')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allTypes')}</SelectItem>
                  {coffeeTypes.map(type => {
                    const translatedType = t(`coffeeTypes.${type}`, { defaultValue: type })
                    return (
                      <SelectItem key={type} value={type}>
                        {translatedType}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.brand')}</label>
              <Select value={filterBrand} onValueChange={handleBrandChange}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allBrands')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBrands')}</SelectItem>
                  {brands.map(brand => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.blend')}</label>
              <Select value={filterBlend} onValueChange={setFilterBlend}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allBlends')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBlends')}</SelectItem>
                  {blends.map(blend => (
                    <SelectItem key={blend} value={blend}>
                      {blend}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.compatibility')}</label>
              <Select value={filterCompatibility} onValueChange={setFilterCompatibility}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allCompatibilities')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCompatibilities')}</SelectItem>
                  {compatibilities.map(compatibility => (
                    <SelectItem key={compatibility} value={compatibility}>
                      {compatibility}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      {loading && products.length === 0 ? (
        /* Loading State */
        <div className="text-center py-16">
          <Loader2 className="h-16 w-16 text-muted-foreground mx-auto mb-4 animate-spin" />
          <h3 className="text-2xl font-semibold mb-2">{t('loading', { defaultValue: 'Loading products...' })}</h3>
          <p className="text-muted-foreground">
            {t('loadingDescription', { defaultValue: 'Please wait while we load the products for you.' })}
          </p>
        </div>
      ) : products.length > 0 ? (
        <section className="mb-16">
          {/* Coffee Products Section */}
          {coffeeProducts.length > 0 && (
            <div className="mb-16">
              <div className="flex items-center gap-3 mb-8">
                <Coffee className="h-8 w-8 text-primary" />
                <h2 className="text-3xl font-bold">{t('coffee')}</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {coffeeProducts.map((product: Product) => (
                  <ProductCard key={product.id} product={product} locale={locale} />
                ))}
              </div>
            </div>
          )}

          {/* Load More Button - positioned after coffee products but before accessories */}
          {hasMore && (
            <div className="flex justify-center py-8 mb-16">
              <Button
                onClick={loadMore}
                disabled={loading}
                variant="outline"
                size="lg"
                className="min-w-[200px]"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    {t('loading', { defaultValue: 'Loading...' })}
                  </>
                ) : (
                  t('loadMore')
                )}
              </Button>
            </div>
          )}

          {/* Accessories Section */}
          {accessoryProducts.length > 0 && (
            <div className="mb-16">
              <div className="flex items-center gap-3 mb-8">
                <ShoppingCart className="h-8 w-8 text-primary" />
                <h2 className="text-3xl font-bold">{t('accessories')}</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {accessoryProducts.map((product: Product) => (
                  <ProductCard key={product.id} product={product} locale={locale} />
                ))}
              </div>
            </div>
          )}
        </section>
      ) : (
        /* Empty State */
        <div className="text-center py-16">
          <Coffee className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-2xl font-semibold mb-2">{t('noProducts')}</h3>
          <p className="text-muted-foreground">
            {t('noProductsDescription')}
          </p>
        </div>
      )}
    </>
  )
}

function ProductCard({ product, locale }: { product: Product; locale: string }) {
  const t = useTranslations('shop.product')
  const hasDiscount = product.discount_price && product.discount_price < product.price
  const displayPrice = hasDiscount ? (product.discount_price || product.price) : product.price
  const costPerEspresso = product.cost_per_espresso

  return (
    <Card className="group hover-lift hover-glow transition-all-smooth">
      <div className="aspect-square relative overflow-hidden rounded-t-lg">
        {product.images && product.images.length > 0 ? (
          <Image
            src={product.images[0]}
            alt={product.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform-smooth"
          />
        ) : (
          <div className="w-full h-full bg-gradient-card flex items-center justify-center">
            <Coffee className="h-16 w-16 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
        )}
        {hasDiscount && (
          <Badge className="absolute top-2 right-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
            -{Math.round(((product.price - (product.discount_price || 0)) / product.price) * 100)}%
          </Badge>
        )}
      </div>
      
      <CardContent className="p-4">
        <CardHeader className="p-0 mb-3">
          <CardTitle className="text-lg font-semibold line-clamp-2 group-hover:text-primary transition-colors">
            {product.title}
          </CardTitle>
          {product.description && (
            <CardDescription className="line-clamp-2">
              {product.description}
            </CardDescription>
          )}
        </CardHeader>
        
        {/* Product Details */}
        <div className="space-y-2 mb-4 text-sm text-muted-foreground">
          {product.brand && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Marca:</span>
              <span>{product.brand}</span>
            </div>
          )}
          {product.pack_quantity && product.pack_quantity > 0 && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Quantità:</span>
              <span>{product.pack_quantity} pz</span>
            </div>
          )}
          {costPerEspresso && costPerEspresso > 0 && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Per espresso:</span>
              <span>{formatCurrency(costPerEspresso)}</span>
            </div>
          )}
        </div>
        
        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold">{formatCurrency(displayPrice)}</span>
            {hasDiscount && (
              <span className="text-sm text-muted-foreground line-through">
                {formatCurrency(product.price)}
              </span>
            )}
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/${locale}/shop/product/${product.id}`}>
              {t('details')}
            </Link>
          </Button>
          <AddToCartButton 
            productId={product.id} 
            variant="outline" 
            size="default"
            className="px-3"
          >
            <ShoppingCart className="h-4 w-4" />
          </AddToCartButton>
        </div>
      </CardContent>
    </Card>
  )
}
